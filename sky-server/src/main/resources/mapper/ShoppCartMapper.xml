<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.ShoppingCartMapper">

    <update id="updateNumberById">
        update shopping_cart set number = #{number} where id = #{id}
    </update>


    <select id="list" resultType="com.sky.entity.ShoppingCart">
        select * from shopping_cart
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="dishId != null">
                and dish_id = #{dishId}
            </if>
            <if test="setmealId != null">
                and setmeal_id = #{setmealId}
            </if>
            <if test="dishFlavor != null and dishFlavor != ''">
                and dish_flavor = #{dishFlavor}
            </if>
        </where>
    </select>

    <insert id="insert" >
        insert into shopping_cart (name, user_id, dish_id, setmeal_id, dish_flavor, number, amount, image, create_time) values (
        #{name}, #{userId}, #{dishId}, #{setmealId}, #{dishFlavor}, #{number}, #{amount}, #{image}, #{createTime})
    </insert>

    <!-- 按ID删除单个商品 -->
    <delete id="deleteById">
        DELETE FROM shopping_cart WHERE id = #{id}
    </delete>

    <!-- 按用户ID删除所有商品 -->
    <delete id="deleteByUserId">
        DELETE FROM shopping_cart WHERE user_id = #{userId}
    </delete>

    <!-- 通用删除方法（按条件删除） -->
    <delete id="delete">
        DELETE FROM shopping_cart
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="dishId != null">
                AND dish_id = #{dishId}
            </if>
            <if test="setmealId != null">
                AND setmeal_id = #{setmealId}
            </if>
            <if test="dishFlavor != null and dishFlavor != ''">
                AND dish_flavor = #{dishFlavor}
            </if>
        </where>
    </delete>

</mapper>

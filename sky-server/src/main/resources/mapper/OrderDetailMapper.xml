<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderDetailMapper">


    
    <insert id="insertBatch">
        insert into order_detail (name, order_id, dish_id, setmeal_id, dish_flavor, number, amount, image) values
        <foreach collection="list" item="orderDetail" separator=",">
            (#{orderDetail.name}, #{orderDetail.orderId}, #{orderDetail.dishId}, #{orderDetail.setmealId}, #{orderDetail.dishFlavor}, #{orderDetail.number}, #{orderDetail.amount}, #{orderDetail.image})
        </foreach>
    </insert>
</mapper>
package com.sky.controller.user;

import com.sky.constant.StatusConstant;
import com.sky.entity.Dish;
import com.sky.result.Result;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 用户端菜品控制器
 * 处理用户端菜品浏览相关的请求
 * 提供菜品查询和展示功能
 */
@RestController("userDishController")
@RequestMapping("/user/dish")
@Slf4j
@Api(tags = "C端-菜品浏览接口")
public class DishController {
    @Autowired
    private DishService dishService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 根据分类ID查询菜品列表
     * 查询指定分类下所有起售状态的菜品，包含菜品的口味信息
     * 用于用户端菜品列表展示，用户可以查看菜品详情和选择口味
     *
     * @param categoryId 分类ID，要查询菜品的分类的唯一标识
     * @return Result<List<DishVO>> 符合条件的菜品列表，包含菜品基本信息和口味信息
     */
    @GetMapping("/list")
    @ApiOperation("根据分类id查询菜品")
    public Result<List<DishVO>> list(Long categoryId) {
        String key="dish_"+categoryId;
        if(redisTemplate.hasKey(key)){
            log.info("从redis中查询");
            List<DishVO> list = (List<DishVO>) redisTemplate.opsForValue().get(key);
            return Result.success(list);
        }
        Dish dish = new Dish();
        dish.setCategoryId(categoryId);
        dish.setStatus(StatusConstant.ENABLE);//查询起售中的菜品
        List<DishVO> list = dishService.listWithFlavor(dish);
        redisTemplate.opsForValue().set(key,list);
        return Result.success(list);
    }

}

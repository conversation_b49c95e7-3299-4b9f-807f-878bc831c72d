package com.sky.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 * 配置Redis序列化器，支持LocalDateTime等Java 8时间类型的序列化
 * 解决Redis存储对象时的序列化问题
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Configuration
@Slf4j
public class RedisConfiguration {

    /**
     * 配置RedisTemplate
     * 设置合适的序列化器，支持LocalDateTime等复杂类型
     *
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate 配置好的Redis模板
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("开始配置Redis序列化器，支持LocalDateTime");

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 创建Jackson2JsonRedisSerializer
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);

        // 配置ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);

        // 关键配置：注册Java 8时间模块，支持LocalDateTime
        objectMapper.registerModule(new JavaTimeModule());
        // 禁用时间戳格式，使用ISO-8601格式
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);

        // 字符串序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // 设置key和value的序列化器
        template.setKeySerializer(stringSerializer);                    // key使用String序列化
        template.setValueSerializer(jackson2JsonRedisSerializer);       // value使用JSON序列化
        template.setHashKeySerializer(stringSerializer);                // hash key使用String序列化
        template.setHashValueSerializer(jackson2JsonRedisSerializer);   // hash value使用JSON序列化

        // 初始化RedisTemplate
        template.afterPropertiesSet();

        log.info("Redis序列化器配置完成，已支持LocalDateTime序列化");
        return template;
    }
}